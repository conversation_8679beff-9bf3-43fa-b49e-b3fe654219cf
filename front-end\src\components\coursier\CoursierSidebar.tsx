import React, { useState } from "react";
import { IconType } from "react-icons";
import { useAuthContext } from "../../contexts/AuthContext";

interface SectionProps {
  icon: IconType;
  label: string;
  link: string;
}

interface CoursierSidebarProps {
  sections: SectionProps[];
  onLinkClick: (link: string) => void;
}

function CoursierSidebar({ sections, onLinkClick }: CoursierSidebarProps) {
  const [activePage, setActivePage] = useState("/coursier/dashboard");
  const { user } = useAuthContext();

  const handleClick = (link: string) => {
    setActivePage(link);
    onLinkClick(link);
  };

  // Organiser les sections par catégories
  const principalSections = sections.filter(section =>
    section.link.includes('/dashboard') || section.link.includes('/profile')
  );

  const servicesSections = sections.filter(section =>
    section.link.includes('/commandes') || section.link.includes('/argent')
  );

  const supportSections = sections.filter(section =>
    section.link.includes('/support')
  );

  const renderSection = (section: SectionProps) => (
    <button
      key={section.link}
      onClick={() => handleClick(section.link)}
      className={`relative flex items-center w-full px-4 py-3 text-left transition-all duration-300 mx-2 mb-1 overflow-hidden ${
        activePage === section.link
          ? 'text-white'
          : 'text-gray-600 hover:bg-gray-100 rounded-lg'
      }`}
      style={activePage === section.link ? {
        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',
        clipPath: 'polygon(0 0, calc(100% - 20px) 0, 100% 100%, 0 100%)',
        transform: 'perspective(100px) rotateX(5deg)',
        boxShadow: '0 8px 16px rgba(59, 130, 246, 0.3), 0 4px 8px rgba(0, 0, 0, 0.1)',
        marginLeft: '4px',
        marginRight: '8px',
        marginBottom: '8px',
        borderRadius: '4px'
      } : {}}
    >
      <div className="flex items-center">
        <section.icon className={`mr-3 text-lg ${
          activePage === section.link ? 'text-white' : 'text-gray-500'
        }`} />
        <span className="font-bold text-sm tracking-wide uppercase">{section.label}</span>
      </div>
      {activePage === section.link && (
        <div
          className="absolute bottom-0 right-0 w-0 h-0"
          style={{
            borderLeft: '20px solid transparent',
            borderBottom: '20px solid rgba(30, 64, 175, 0.8)',
            transform: 'translateY(100%)'
          }}
        />
      )}
    </button>
  );

  return (
    <div className="flex flex-col w-64 h-full bg-white shadow-lg">
      {/* User Profile Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
            {user?.profileImage ? (
              <img
                src={user.profileImage}
                alt={user.name}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <span className="text-white font-medium text-sm">
                {user?.name?.charAt(0) || 'C'}
              </span>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user?.name || 'Coursier'}
            </p>
            <p className="text-xs text-gray-500">Coursier</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 py-4">
        {/* Section PRINCIPAL */}
        {principalSections.length > 0 && (
          <div className="mb-6">
            <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              PRINCIPAL
            </h3>
            {principalSections.map(renderSection)}
          </div>
        )}

        {/* Section SERVICES */}
        {servicesSections.length > 0 && (
          <div className="mb-6">
            <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              SERVICES
            </h3>
            {servicesSections.map(renderSection)}
          </div>
        )}

        {/* Section SUPPORT */}
        {supportSections.length > 0 && (
          <div className="mb-6">
            <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              SUPPORT
            </h3>
            {supportSections.map(renderSection)}
          </div>
        )}
      </div>
    </div>
  );
}

export default CoursierSidebar;
