import React, { useState } from "react";
import { IconType } from "react-icons";
import { useAuthContext } from "../../contexts/AuthContext";

interface SectionProps {
  icon: IconType;
  label: string;
  link: string;
}

interface EntrepriseSidebarProps {
  sections: SectionProps[];
  onLinkClick: (link: string) => void;
}

function EntrepriseSidebar({ sections, onLinkClick }: EntrepriseSidebarProps) {
  const [activePage, setActivePage] = useState("/entreprise/dashboardmanagement");
  const { user } = useAuthContext();

  const handleClick = (link: string) => {
    setActivePage(link);
    onLinkClick(link);
  };

  // Organiser les sections par catégories
  const principalSections = sections.filter(section =>
    section.link.includes('/dashboardmanagement') || section.link.includes('/profile')
  );

  const servicesSections = sections.filter(section =>
    section.link.includes('/creationmanagement') ||
    section.link.includes('/domicialisationmanagement') ||
    section.link.includes('/annoncesmanagement') ||
    section.link.includes('/comptabilitemanagement')
  );

  const gestionSections = sections.filter(section =>
    section.link.includes('/offermanagement') ||
    section.link.includes('/documents') ||
    section.link.includes('/support')
  );

  const renderSection = (section: SectionProps) => (
    <button
      key={section.link}
      onClick={() => handleClick(section.link)}
      className={`relative flex items-center w-full px-4 py-3 text-left transition-all duration-200 mx-2 mb-1 overflow-hidden ${
        activePage === section.link
          ? 'text-white'
          : 'text-gray-600 hover:bg-gray-100 rounded-lg'
      }`}
      style={activePage === section.link ? {
        background: 'linear-gradient(135deg, #f97316 0%, #ea580c 100%)',
        clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 100%, 12px 100%)',
        transform: 'skew(-5deg)',
        marginLeft: '8px',
        marginRight: '16px'
      } : {}}
    >
      <div className={activePage === section.link ? 'transform skew(5deg)' : ''}>
        <section.icon className={`mr-3 text-lg ${
          activePage === section.link ? 'text-white' : 'text-gray-500'
        }`} />
        <span className="font-medium">{section.label}</span>
      </div>
    </button>
  );

  return (
    <div className="flex flex-col w-64 h-full bg-white shadow-lg">
      {/* User Profile Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
          <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
            {user?.profileImage ? (
              <img
                src={user.profileImage}
                alt={user.name}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <span className="text-white font-medium text-sm">
                {user?.name?.charAt(0) || 'E'}
              </span>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user?.name || 'Entreprise'}
            </p>
            <p className="text-xs text-gray-500">Entreprise</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 py-4">
        {/* Section PRINCIPAL */}
        {principalSections.length > 0 && (
          <div className="mb-6">
            <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              PRINCIPAL
            </h3>
            {principalSections.map(renderSection)}
          </div>
        )}

        {/* Section SERVICES */}
        {servicesSections.length > 0 && (
          <div className="mb-6">
            <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              SERVICES
            </h3>
            {servicesSections.map(renderSection)}
          </div>
        )}

        {/* Section GESTION */}
        {gestionSections.length > 0 && (
          <div className="mb-6">
            <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              GESTION
            </h3>
            {gestionSections.map(renderSection)}
          </div>
        )}
      </div>
    </div>
  );
}

export default EntrepriseSidebar;
