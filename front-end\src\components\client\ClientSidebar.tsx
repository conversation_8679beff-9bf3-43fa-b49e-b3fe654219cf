import React, { useState } from "react";
import { IconType } from "react-icons";
import { useAuthContext } from "../../contexts/AuthContext";

interface SectionProps {
  icon: IconType;
  label: string;
  link: string;
}

interface ClientSidebarProps {
  sections: SectionProps[];
  onLinkClick: (link: string) => void;
}

function ClientSidebar({ sections, onLinkClick }: ClientSidebarProps) {
  const [activePage, setActivePage] = useState("/client/dashboard");
  const { user } = useAuthContext();

  const handleClick = (link: string) => {
    setActivePage(link);
    onLinkClick(link);
  };

  // Organiser les sections par catégories
  const principalSections = sections.filter(section =>
    section.link.includes('/dashboard') || section.link.includes('/profile')
  );

  const servicesSections = sections.filter(section =>
    section.link.includes('/comptabilite') ||
    section.link.includes('/domiciliation') ||
    section.link.includes('/offres')
  );

  const gestionSections = sections.filter(section =>
    section.link.includes('/notifications')
  );

  const renderSection = (section: SectionProps) => (
    <button
      key={section.link}
      onClick={() => handleClick(section.link)}
      className={`flex items-center w-full px-4 py-3 text-left transition-all duration-200 rounded-lg mx-2 mb-1 ${
        activePage === section.link
          ? 'bg-green-500 text-white'
          : 'text-gray-600 hover:bg-gray-100'
      }`}
    >
      <section.icon className={`mr-3 text-lg ${
        activePage === section.link ? 'text-white' : 'text-gray-500'
      }`} />
      <span className="font-medium">{section.label}</span>
    </button>
  );

  return (
    <div className="flex flex-col w-64 h-full bg-white shadow-lg">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">M</span>
          </div>
          <span className="font-bold text-gray-800">Modernize</span>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 py-4">
        {/* Section PRINCIPAL */}
        {principalSections.length > 0 && (
          <div className="mb-6">
            <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              PRINCIPAL
            </h3>
            {principalSections.map(renderSection)}
          </div>
        )}

        {/* Section SERVICES */}
        {servicesSections.length > 0 && (
          <div className="mb-6">
            <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              SERVICES
            </h3>
            {servicesSections.map(renderSection)}
          </div>
        )}

        {/* Section GESTION */}
        {gestionSections.length > 0 && (
          <div className="mb-6">
            <h3 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              GESTION
            </h3>
            {gestionSections.map(renderSection)}
          </div>
        )}
      </div>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
          <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
            {user?.profileImage ? (
              <img
                src={user.profileImage}
                alt={user.name}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <span className="text-white font-medium text-sm">
                {user?.name?.charAt(0) || 'U'}
              </span>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user?.name || 'Utilisateur'}
            </p>
            <p className="text-xs text-gray-500">Client</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ClientSidebar;
