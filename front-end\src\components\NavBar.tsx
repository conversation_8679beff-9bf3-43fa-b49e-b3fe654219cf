import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ser, FaSignOutAlt, FaChevronDown, FaEnvelope, FaTasks } from "react-icons/fa";
import logo from "../assets/logo.png";
import ThemeSwitcher from "./ThemeSwitcher";
import { useNavigate } from "react-router-dom";
import { useAuthContext } from "../contexts/AuthContext";

interface Props {
  title: string;
  userName?: string;
  userImage?: string; // Gardé pour compatibilité mais non utilisé
  onProfileClick?: (path: string) => void;
}


const NavBar = ({ title, userName, onProfileClick }: Props) => {
  const navigate = useNavigate();
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const { user, logout } = useAuthContext();

  // Référence pour le menu déroulant
  const menuRef = useRef<HTMLDivElement>(null);

  // Utiliser les informations de l'utilisateur du contexte si disponibles
  const displayName = userName || user?.name || "Utilisateur";

  // Utiliser une couleur différente pour chaque rôle
  let avatarColor = "bg-blue-500";

  if (user?.role === 'client') {
    avatarColor = "bg-green-500";
  } else if (user?.role === 'coursier') {
    avatarColor = "bg-purple-500";
  } else if (user?.role === 'entreprise') {
    avatarColor = "bg-[#5b99c2]";
  }

  // Fermer le menu lorsque l'utilisateur clique en dehors
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowProfileMenu(false);
      }
    };

    // Ajouter l'écouteur d'événement lorsque le menu est ouvert
    if (showProfileMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Nettoyer l'écouteur d'événement
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showProfileMenu]);

  const handleLogout = () => {
    // Utiliser la fonction de déconnexion du contexte d'authentification
    logout();

    // Rediriger vers la page d'accueil
    navigate('/');
  };

  return (
    <div className="flex items-center justify-between w-full bg-white border-b border-gray-200 px-4 py-2">
      {/* Left side - Logo and Navigation */}
      <div className="flex items-center space-x-6">
        {/* Logo */}
        <div className="flex-none">
          <button onClick={() => navigate("/")}>
            <img src={logo} alt="logo" className="h-14" />
          </button>
        </div>

        {/* Title */}
        <div className="text-lg font-semibold text-gray-800">
          {title || "Dashboard"}
        </div>
      </div>

      {/* Right side - Icons */}
      <div className="flex items-center space-x-3">
        {/* Notification Button */}
        <button className="p-1 rounded-full text-gray-500 hover:bg-gray-100">
          <FaBell className="h-5 w-5" />
        </button>

        {/* Settings Button */}
        <button className="p-1 rounded-full text-gray-500 hover:bg-gray-100">
          <FaCog className="h-5 w-5" />
        </button>

        {/* Theme Switcher */}
        <ThemeSwitcher />

        {/* Profile Dropdown */}
        <div className="relative" ref={menuRef}>
          <button
            className="p-1 rounded-full hover:bg-gray-100 focus:outline-none transition-colors"
            onClick={() => setShowProfileMenu(!showProfileMenu)}
          >
            {/* Image de profil - Affichage de l'initiale */}
            <div className={`h-10 w-10 rounded-full border-2 border-gray-200 overflow-hidden ${avatarColor} flex items-center justify-center shadow-sm`}>
              {user?.profileImage ? (
                <img
                  src={user.profileImage}
                  alt={displayName}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-white text-sm font-bold">
                  {displayName.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
          </button>

          {/* Menu déroulant */}
          {showProfileMenu && (
            <div className="absolute right-0 mt-3 w-56 bg-white rounded-lg shadow-xl py-2 z-50 border border-gray-100">
              {/* Avatar et nom en haut */}
              <div className="flex items-center px-4 py-3 border-b border-gray-100">
                <div className={`h-10 w-10 rounded-full ${avatarColor} flex items-center justify-center mr-3`}>
                  {user?.profileImage ? (
                    <img
                      src={user.profileImage}
                      alt={displayName}
                      className="w-full h-full object-cover rounded-full"
                    />
                  ) : (
                    <span className="text-white text-sm font-bold">
                      {displayName.charAt(0).toUpperCase()}
                    </span>
                  )}
                </div>
                <div>
                  <div className="font-medium text-gray-900 text-sm">{displayName}</div>
                  <div className="text-xs text-gray-500 capitalize">{user?.role || 'Utilisateur'}</div>
                </div>
              </div>

              {/* Menu items */}
              <div className="py-1">
                <button
                  className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  onClick={() => {
                    // Déterminer le type d'utilisateur en fonction de l'URL actuelle
                    const currentPath = window.location.pathname;
                    let profilePath = '/profile';

                    if (currentPath.includes('/client/')) {
                      profilePath = '/client/profile';
                    } else if (currentPath.includes('/coursier/')) {
                      profilePath = '/coursier/profile';
                    } else if (currentPath.includes('/entreprise/')) {
                      profilePath = '/entreprise/profile';
                    }

                    // Si la fonction de callback est fournie, l'utiliser pour ouvrir la page dans l'iframe
                    if (onProfileClick) {
                      onProfileClick(profilePath);
                    } else {
                      // Sinon, naviguer directement vers la page
                      navigate(profilePath);
                    }

                    setShowProfileMenu(false);
                  }}
                >
                  <FaUser className="mr-3 text-gray-400 text-sm" />
                  <span>My Profile</span>
                </button>

                <button
                  className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  onClick={() => {
                    // Logique pour My Account
                    setShowProfileMenu(false);
                  }}
                >
                  <FaEnvelope className="mr-3 text-gray-400 text-sm" />
                  <span>My Account</span>
                </button>

                <button
                  className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  onClick={() => {
                    // Logique pour My Tasks
                    setShowProfileMenu(false);
                  }}
                >
                  <FaTasks className="mr-3 text-gray-400 text-sm" />
                  <span>My Tasks</span>
                </button>
              </div>

              {/* Logout button */}
              <div className="border-t border-gray-100 pt-1">
                <button
                  className="flex items-center justify-center w-full mx-4 my-2 px-4 py-2 text-sm text-blue-600 border border-blue-200 rounded-md hover:bg-blue-50 transition-colors"
                  onClick={handleLogout}
                >
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NavBar;

